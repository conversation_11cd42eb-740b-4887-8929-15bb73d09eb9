<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瑜伽加载组件演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 演示页面背景 */
        .demo-bg {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        /* 加载框遮罩层 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(8px);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            animation: fadeIn 0.3s ease-out forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* 加载框主体 */
        .loading-modal {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 24px;
            padding: 32px;
            max-width: 320px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transform: scale(0.8) translateY(20px);
            animation: modalSlideIn 0.4s ease-out 0.1s forwards;
        }

        @keyframes modalSlideIn {
            to {
                transform: scale(1) translateY(0);
            }
        }

        .breathing-animation {
            animation: breathe 4s ease-in-out infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }

        .lotus-spin {
            animation: lotus-rotate 8s linear infinite;
        }

        @keyframes lotus-rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .progress-bar {
            animation: progress 3s ease-in-out infinite;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .ripple {
            position: relative;
            overflow: hidden;
        }

        .ripple::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.2);
            transform: translate(-50%, -50%);
            animation: ripple-effect 3s infinite;
        }

        @keyframes ripple-effect {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
            }
            100% {
                width: 200px;
                height: 200px;
                opacity: 0;
            }
        }
        
        /* 隐藏滚动条但保持功能 */
        .loading-overlay::-webkit-scrollbar {
            display: none;
        }

        .loading-overlay {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body class="demo-bg min-h-screen p-8">
    <!-- 演示页面内容 -->
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">瑜伽加载组件演示</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4">组件特性</h2>
                <ul class="space-y-2 text-gray-600">
                    <li>✨ 苹果风格设计语言</li>
                    <li>🎯 模态框形式的加载组件</li>
                    <li>🌊 流畅的动画效果</li>
                    <li>📱 响应式设计</li>
                    <li>🎨 毛玻璃效果</li>
                </ul>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <h2 class="text-xl font-semibold mb-4">使用方法</h2>
                <div class="space-y-4">
                    <button onclick="showLoading()" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-6 rounded-lg transition-colors">
                        显示加载框
                    </button>
                    <button onclick="hideLoading()" class="w-full bg-gray-500 hover:bg-gray-600 text-white py-3 px-6 rounded-lg transition-colors">
                        隐藏加载框
                    </button>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl p-6 shadow-lg">
            <h2 class="text-xl font-semibold mb-4">代码示例</h2>
            <pre class="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto"><code>// 显示加载框
showLoading();

// 隐藏加载框
hideLoading();

// 自定义加载文本
showLoading('正在处理您的请求...');</code></pre>
        </div>
    </div>

    <!-- 瑜伽加载组件 -->
    <div id="yogaLoading" class="loading-overlay" style="display: none;">
        <div class="loading-modal">
            <!-- 涟漪效果 -->
            <div class="ripple absolute inset-0 rounded-3xl"></div>

            <!-- 主要内容 -->
            <div class="relative z-10">
                <!-- Logo 区域 -->
                <div class="mb-6 fade-in-up" style="animation-delay: 0.2s;">
                    <div class="w-20 h-20 mx-auto mb-3 relative">
                        <div class="breathing-animation w-full h-full bg-gradient-to-br from-purple-400 to-blue-500 bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-spa text-3xl text-purple-600 lotus-spin"></i>
                        </div>
                    </div>
                    <h1 class="text-xl font-medium text-gray-800 mb-1">瑜伽冥想</h1>
                    <p class="text-gray-600 text-sm">找到内心的平静</p>
                </div>

                <!-- 加载动画 -->
                <div class="mb-6 fade-in-up" style="animation-delay: 0.4s;">
                    <div class="w-12 h-12 mx-auto mb-3 relative">
                        <div class="absolute inset-0 border-3 border-gray-200 rounded-full"></div>
                        <div class="absolute inset-0 border-3 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
                    </div>
                    <p class="text-gray-700 text-base font-medium mb-2" id="loading-text">正在准备您的瑜伽之旅...</p>
                </div>

                <!-- 进度条 -->
                <div class="mb-6 fade-in-up" style="animation-delay: 0.6s;">
                    <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
                        <div class="h-2 rounded-full progress-bar"></div>
                    </div>
                    <p class="text-gray-500 text-sm" id="progress-text">0%</p>
                </div>

                <!-- 功能预览 -->
                <div class="grid grid-cols-3 gap-3 mb-4 fade-in-up" style="animation-delay: 0.8s;">
                    <div class="floating text-center" style="animation-delay: 0s;">
                        <div class="w-10 h-10 mx-auto mb-1 bg-gradient-to-br from-pink-400 to-red-400 bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-heart text-pink-500 text-sm"></i>
                        </div>
                        <p class="text-gray-600 text-xs">冥想</p>
                    </div>
                    <div class="floating text-center" style="animation-delay: 0.5s;">
                        <div class="w-10 h-10 mx-auto mb-1 bg-gradient-to-br from-green-400 to-blue-400 bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-leaf text-green-500 text-sm"></i>
                        </div>
                        <p class="text-gray-600 text-xs">呼吸</p>
                    </div>
                    <div class="floating text-center" style="animation-delay: 1s;">
                        <div class="w-10 h-10 mx-auto mb-1 bg-gradient-to-br from-indigo-400 to-purple-400 bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-moon text-indigo-500 text-sm"></i>
                        </div>
                        <p class="text-gray-600 text-xs">放松</p>
                    </div>
                </div>

                <!-- 底部提示 -->
                <div class="fade-in-up" style="animation-delay: 1s;">
                    <p class="text-gray-400 text-xs">
                        <i class="fas fa-info-circle mr-1"></i>
                        请保持网络连接稳定
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 瑜伽加载组件类
        class YogaLoader {
            constructor() {
                this.overlay = document.getElementById('yogaLoading');
                this.loadingText = document.getElementById('loading-text');
                this.progressText = document.getElementById('progress-text');
                this.isVisible = false;
                this.progress = 0;
                this.loadingTexts = [
                    '正在准备您的瑜伽之旅...',
                    '加载冥想音乐...',
                    '准备呼吸指导...',
                    '设置放松环境...',
                    '即将完成...'
                ];
                this.currentTextIndex = 0;
                this.progressInterval = null;
            }

            show(customText = null) {
                if (this.isVisible) return;

                this.isVisible = true;
                this.progress = 0;
                this.currentTextIndex = 0;

                if (customText) {
                    this.loadingText.textContent = customText;
                } else {
                    this.loadingText.textContent = this.loadingTexts[0];
                }

                this.progressText.textContent = '0%';
                this.overlay.style.display = 'flex';

                // 开始进度模拟
                this.startProgress();

                // 添加淡入动画
                const elements = this.overlay.querySelectorAll('.fade-in-up');
                elements.forEach((el, index) => {
                    setTimeout(() => {
                        el.style.animationPlayState = 'running';
                    }, index * 200);
                });
            }

            hide() {
                if (!this.isVisible) return;

                this.isVisible = false;
                this.stopProgress();

                this.overlay.style.animation = 'fadeOut 0.3s ease-out forwards';

                setTimeout(() => {
                    this.overlay.style.display = 'none';
                    this.overlay.style.animation = '';
                }, 300);
            }

            startProgress() {
                this.progressInterval = setInterval(() => {
                    this.progress += Math.random() * 15 + 5;
                    if (this.progress > 100) this.progress = 100;

                    this.progressText.textContent = Math.round(this.progress) + '%';

                    // 更新加载文本
                    const textThreshold = 20 * (this.currentTextIndex + 1);
                    if (this.progress >= textThreshold && this.currentTextIndex < this.loadingTexts.length - 1) {
                        this.currentTextIndex++;
                        this.loadingText.textContent = this.loadingTexts[this.currentTextIndex];
                    }

                    if (this.progress >= 100) {
                        this.stopProgress();
                        setTimeout(() => {
                            this.loadingText.textContent = '准备就绪！正在进入...';
                        }, 500);
                    }
                }, 200);
            }

            stopProgress() {
                if (this.progressInterval) {
                    clearInterval(this.progressInterval);
                    this.progressInterval = null;
                }
            }
        }

        // 创建全局实例
        const yogaLoader = new YogaLoader();

        // 全局函数供外部调用
        function showLoading(customText = null) {
            yogaLoader.show(customText);
        }

        function hideLoading() {
            yogaLoader.hide();
        }

        // 触摸反馈效果
        function addTouchFeedback() {
            const modal = document.querySelector('.loading-modal');

            modal.addEventListener('touchstart', function(e) {
                this.style.transform = 'scale(0.98)';
                this.style.transition = 'transform 0.1s ease';
            });

            modal.addEventListener('touchend', function(e) {
                this.style.transform = 'scale(1)';
            });
        }

        // 添加fadeOut动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTouchFeedback();

            // 演示：3秒后自动显示加载框
            setTimeout(() => {
                showLoading();

                // 5秒后自动隐藏
                setTimeout(() => {
                    hideLoading();
                }, 5000);
            }, 1000);
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            // 处理屏幕旋转和尺寸变化
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        });
    </script>
</body>
</html>
